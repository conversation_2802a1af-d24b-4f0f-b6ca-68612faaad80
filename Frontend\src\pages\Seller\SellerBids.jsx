import React from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectBids } from "../../redux/slices/sellerDashboardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import { IoEyeSharp } from "react-icons/io5";
import Table from "../../components/common/Table";
import "../../styles/SellerBids.css";
import { SlEye } from "react-icons/sl";
const SellerBids = () => {
  const bids = useSelector(selectBids);
  const navigate = useNavigate();

  const handleViewDetails = (bidId) => {
    navigate(`/seller/bid-details/${bidId.replace('#', '')}`);
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    { key: "id", label: "Bid Id" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <img src={item.image} alt={item.title} />
          <span>{item.title}</span>
        </div>
      ),
    },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    { key: "bidAmount", label: "Bid Amount" },
    {
      key: "action",
      label: "Action",
      render: (item) => (
        <SlEye
          className="threedoticon"
          onClick={() => handleViewDetails(item.id)}
          style={{ cursor: 'pointer' }}
        />
      ),
    },
  ];

  const formatData = (bids) => {
    return bids.map((item, index) => ({
      ...item,
      no: index + 1,
      date: `${item.date} | 4:50PM`,
    }));
  };

  return (
    <SellerLayout>
      <div className="seller-bids-container">
        <Table
          columns={columns}
          data={formatData(bids)}
          className="bids-table"
        />
      </div>
    </SellerLayout>
  );
};

export default SellerBids;
